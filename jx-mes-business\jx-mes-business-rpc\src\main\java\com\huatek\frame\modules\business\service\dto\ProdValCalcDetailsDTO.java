package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 产值计算明细DTO 实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("产值计算明细DTO实体类")
public class ProdValCalcDetailsDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 产值计算id
     **/
    @ApiModelProperty("产值计算id")
    private String productionValueCalculationId;
    
    /**
	 * 试验项目
     **/
    @ApiModelProperty("试验项目")
    private String experimentProject;
    
    /**
	 * 数量
     **/
    @ApiModelProperty("数量")
    private Long quantity;
    
    /**
	 * 单价
     **/
    @ApiModelProperty("单价")
    private BigDecimal unitPrice;
    
    /**
	 * 合计
     **/
    @ApiModelProperty("合计")
    private BigDecimal total;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;


}