package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 产值计算明细VO实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("产值计算明细DTO实体类")
public class ProdValCalcDetailsVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 产值计算id
     **/
    @ApiModelProperty("产值计算id")
    @Excel(name = "产值计算id",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productionValueCalculationId;

    /**
	 * 试验项目
     **/
    @ApiModelProperty("试验项目")
    @Excel(name = "试验项目",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String experimentProject;

    /**
	 * 数量
     **/
    @ApiModelProperty("数量")
    @Excel(name = "数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long quantity;

    /**
	 * 单价
     **/
    @ApiModelProperty("单价")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "单价",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal unitPrice;

    /**
	 * 合计
     **/
    @ApiModelProperty("合计")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "合计",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal total;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}