package com.huatek.frame.modules.business.domain;

import com.baomidou.mybatisplus.annotation.*;
import java.io.Serializable;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;

import lombok.Getter;
import lombok.Setter;

/**
* @description 工时报表
* <AUTHOR>
* @date 2025-08-18
**/
@Setter
@Getter
@TableName("work_report_table")
public class WorkReportTable implements Serializable {

    
    @TableId(type = IdType.ASSIGN_ID)
    private String id;

    
    /**
	 * 工单编号
     **/
    @TableField(value = "work_order_number"
    )
    private String workOrderNumber;

    
    /**
	 * 送检数量
     **/
    @TableField(value = "inspection_quantity2"
    )
    private Long inspectionQuantity2;

    
    /**
	 * 标准工序名称
     **/
    @TableField(value = "standard_process_name"
    )
    private String standardProcessName;

    
    /**
	 * 客户试验名称
     **/
    @TableField(value = "customer_test_name"
    )
    private String customerTestName;

    
    /**
	 * 产品名称
     **/
    @TableField(value = "product_name"
    )
    private String productName;

    
    /**
	 * 产品型号
     **/
    @TableField(value = "product_model"
    )
    private String productModel;

    
    /**
	 * 生产厂家
     **/
    @TableField(value = "manufacturer"
    )
    private String manufacturer;

    
    /**
	 * 批次号
     **/
    @TableField(value = "batch_number"
    )
    private String batchNumber;

    
    /**
	 * 产品分类
     **/
    @TableField(value = "product_category"
    )
    private String productCategory;

    
    /**
	 * 产品资料
     **/
    @TableField(value = "product_information1"
    )
    private String productInformation1;

    
    /**
	 * 委托单位
     **/
    @TableField(value = "entrusted_unit"
    )
    private String entrustedUnit;

    
    /**
	 * 试验方式
     **/
    @TableField(value = "test_methodology"
    )
    private String testMethodology;

    
    /**
	 * 工序开始时间
     **/
    @TableField(value = "process_start_time"
    )
    private Timestamp processStartTime;

    
    /**
	 * 工序结束时间
     **/
    @TableField(value = "process_end_time"
    )
    private Timestamp processEndTime;

    
    /**
	 * 工序时长
     **/
    @TableField(value = "process_duration"
    )
    private BigDecimal processDuration;

    
    /**
	 * 报工人
     **/
    @TableField(value = "reporter4"
    )
    private String reporter4;

    
    /**
	 * 研发任务编号
     **/
    @TableField(value = "rd_task_number"
    )
    private String rdTaskNumber;

    
    /**
	 * 是否参与核算
     **/
    @TableField(value = "wipaia"
    )
    private String wipaia;

    
    /**
	 * 是否外协工序
     **/
    @TableField(value = "whether_outsourced_process6"
    )
    private String whetherOutsourcedProcess6;

    
    /**
	 * 设备共用工单
     **/
    @TableField(value = "shared_equipment_work_order"
    )
    private String sharedEquipmentWorkOrder;

    
    /**
	 * 订单编号
     **/
    @TableField(value = "order_number"
    )
    private String orderNumber;

    
    /**
	 * 样本总数
     **/
    @TableField(value = "sample_total_count"
    )
    private Long sampleTotalCount;

    
    /**
	 * 设备编号
     **/
    @TableField(value = "device_serial_number"
    )
    private String deviceSerialNumber;

    
    /**
	 * 设备名称
     **/
    @TableField(value = "device_name"
    )
    private String deviceName;

    
    /**
	 * 固定资产编码
     **/
    @TableField(value = "fixed_asset_coding"
    )
    private String fixedAssetCoding;

    
    /**
	 * 同工序时长
     **/
    @TableField(value = "duration_of_the_same_process"
    )
    private String durationOfTheSameProcess;

    
    /**
	 * 设备开始时间
     **/
    @TableField(value = "equipment_end_time"
    )
    private Timestamp equipmentEndTime;

    
    /**
	 * 设备结束时间
     **/
    @TableField(value = "equipment_end_time27"
    )
    private Timestamp equipmentEndTime27;

    
    /**
	 * 设备用时（h）
     **/
    @TableField(value = "equipment_running_timeh"
    )
    private BigDecimal equipmentRunningTimeh;

    
    /**
	 * 设备能耗
     **/
    @TableField(value = "equipment_energy_consumption"
    )
    private Long equipmentEnergyConsumption;

    
    /**
	 * 设备功耗
     **/
    @TableField(value = "equipment_power_consumption"
    )
    private Long equipmentPowerConsumption;

    
    /**
	 * 创建人
     **/
    @TableField(value = "codex_torch_creator_id"
    )
    private String codexTorchCreatorId;

    
    /**
	 * 更新人
     **/
    @TableField(value = "codex_torch_updater"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private String codexTorchUpdater;

    
    /**
	 * 所属组织
     **/
    @TableField(value = "codex_torch_group_id"
    )
    private String codexTorchGroupId;

    
    /**
	 * 创建时间
     **/
    @TableField(value = "codex_torch_create_datetime"
            ,fill = FieldFill.INSERT
    )
    private Timestamp codexTorchCreateDatetime;

    
    /**
	 * 更新时间
     **/
    @TableField(value = "codex_torch_update_datetime"
            ,fill = FieldFill.INSERT_UPDATE
    )
    private Timestamp codexTorchUpdateDatetime;

    
    /**
	 * 已删除
     **/
    @TableField(value = "codex_torch_deleted"
    )
    private String codexTorchDeleted;
}