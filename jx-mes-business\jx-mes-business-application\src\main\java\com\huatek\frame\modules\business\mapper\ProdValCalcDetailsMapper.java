package com.huatek.frame.modules.business.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.Page;
import java.util.List;

import com.huatek.frame.modules.business.domain.ProdValCalcDetails;
import com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO;
import com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO;
import com.huatek.frame.modules.system.domain.vo.SelectOptionsVO;
import io.swagger.annotations.ApiModelProperty;
import org.apache.ibatis.annotations.Param;

/**
* 产值计算明细mapper
* <AUTHOR>
* @date 2025-08-22
**/
public interface ProdValCalcDetailsMapper extends BaseMapper<ProdValCalcDetails> {

     /**
	 * 产值计算明细分页
	 * @param dto
	 * @return
	 */
	Page<ProdValCalcDetailsVO> selectProdValCalcDetailsPage(ProdValCalcDetailsDTO dto);


    /**
     * 根据条件查询产值计算明细列表
     *
     * @param dto 产值计算明细信息
     * @return 产值计算明细集合信息
     */
    List<ProdValCalcDetailsVO> selectProdValCalcDetailsList(ProdValCalcDetailsDTO dto);

	/**
	 * 根据IDS查询产值计算明细列表
	 * @param ids
	 * @return
	 */
    List<ProdValCalcDetailsVO> selectProdValCalcDetailsListByIds(@Param("ids") List<String> ids);

    /**
     * 根据产值计算id查询产值计算明细列表
     *
     * @param productionValueCalculationId 产值计算id
     * @return 产值计算明细列表
     */
    List<ProdValCalcDetailsVO> selectProdValCalcDetailsListByProductionValueCalculationId(@Param("productionValueCalculationId") String productionValueCalculationId);

}