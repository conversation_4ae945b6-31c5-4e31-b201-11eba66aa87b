<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.FileReviewMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.work_order as workOrder,
<!--		t.product_name as productName,-->
<!--		t.product_model as productModel,-->
<!--		t.manufacturer as manufacturer,-->
<!--		t.product_category as productCategory,-->
<!--		t.standard_specification_number as standardSpecificationNumber,-->
		t.assoc_exception_feedback_num as assocExceptionFeedbackNum,
		t.change_file as changeFile,
		t.review_result as reviewResult,
		t.review_remark as reviewRemark,
		t.reviewer as reviewer,
		t.review_time as reviewTime,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectFileReviewPage" parameterType="com.huatek.frame.modules.business.service.dto.FileReviewDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.FileReviewVO">
		select
		<include refid="Base_Column_List" />,pl.product_name as productName,
        pl.product_model as productModel,
        pl.manufacturer as manufacturer,
        pl.product_category as productCategory,
        (
        SELECT GROUP_CONCAT(ss2.specification_number)
        FROM standard_specification ss2
        WHERE FIND_IN_SET(ss2.id, pl.standard_specification_id) > 0
        ) as standardSpecificationNumber
			from file_review t left join product_list pl on t.product  = pl.id
        <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrder != null and workOrder != ''">
                    and t.work_order  = #{workOrder}
                </if>
                <if test="productName != null and productName != ''">
                    and pl.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and pl.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and pl.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pl.product_category  like concat('%', #{productCategory} ,'%')
                </if>

                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="changeFile != null and changeFile != ''">
                    and t.change_file  like concat('%', #{changeFile} ,'%')
                </if>
                <if test="reviewResult != null and reviewResult != ''">
                    and t.review_result  = #{reviewResult}
                </if>
                <if test="reviewRemark != null and reviewRemark != ''">
                    and t.review_remark  like concat('%', #{reviewRemark} ,'%')
                </if>
                <if test="reviewer != null and reviewer != ''">
                    and t.reviewer  like concat('%', #{reviewer} ,'%')
                </if>
                <if test="reviewTime != null and reviewTime != ''">
                    and t.review_time  like concat('%', #{reviewTime} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>
     <select id="selectOptionsByWorkOrder" parameterType="String"
        resultType="com.huatek.frame.modules.system.domain.vo.SelectOptionsVO">
        select
            t.work_order_number label,
        	t.work_order_number value
        from production_order t
        WHERE t.work_order_number != ''
         and t.codex_torch_deleted = '0'
     </select>

    <select id="selectFileReviewList" parameterType="com.huatek.frame.modules.business.service.dto.FileReviewDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.FileReviewVO">
		select
		<include refid="Base_Column_List" />,pl.product_name as productName,
        pl.product_model as productModel,
        pl.manufacturer as manufacturer,
        pl.product_category as productCategory,
        (
        SELECT GROUP_CONCAT(ss2.specification_number)
        FROM standard_specification ss2
        WHERE FIND_IN_SET(ss2.id, pl.standard_specification_id) > 0
        ) as standardSpecificationNumber
        from file_review t left join product_list pl on t.product  = pl.id
            <where>
                and t.codex_torch_deleted = '0'
                <if test="workOrder != null and workOrder != ''">
                    and t.work_order  = #{workOrder}
                </if>
                <if test="productName != null and productName != ''">
                    and t.product_name  like concat('%', #{productName} ,'%')
                </if>
                <if test="productModel != null and productModel != ''">
                    and pl.product_model  like concat('%', #{productModel} ,'%')
                </if>
                <if test="manufacturer != null and manufacturer != ''">
                    and pl.manufacturer  like concat('%', #{manufacturer} ,'%')
                </if>
                <if test="productCategory != null and productCategory != ''">
                    and pl.product_category  like concat('%', #{productCategory} ,'%')
                </if>

                <if test="assocExceptionFeedbackNum != null and assocExceptionFeedbackNum != ''">
                    and t.assoc_exception_feedback_num  like concat('%', #{assocExceptionFeedbackNum} ,'%')
                </if>
                <if test="changeFile != null and changeFile != ''">
                    and t.change_file  like concat('%', #{changeFile} ,'%')
                </if>
                <if test="reviewResult != null and reviewResult != ''">
                    and t.review_result  = #{reviewResult}
                </if>
                <if test="reviewRemark != null and reviewRemark != ''">
                    and t.review_remark  like concat('%', #{reviewRemark} ,'%')
                </if>
                <if test="reviewer != null and reviewer != ''">
                    and t.reviewer  like concat('%', #{reviewer} ,'%')
                </if>
                <if test="reviewTime != null and reviewTime != ''">
                    and t.review_time  like concat('%', #{reviewTime} ,'%')
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectFileReviewListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.FileReviewVO">
		select
		<include refid="Base_Column_List" />,pl.product_name as productName,
        pl.product_model as productModel,
        pl.manufacturer as manufacturer,
        pl.product_category as productCategory,
        (
        SELECT GROUP_CONCAT(ss2.specification_number)
        FROM standard_specification ss2
        WHERE FIND_IN_SET(ss2.id, pl.standard_specification_id) > 0
        ) as standardSpecificationNumber
        from file_review t left join product_list pl on t.product  = pl.id
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
</mapper>