package com.huatek.frame.modules.business.service;


import com.huatek.frame.common.response.TorchResponse;
import com.huatek.frame.modules.business.domain.ProductionOrder;
import com.huatek.frame.modules.business.domain.vo.FileReviewVO;
import com.huatek.frame.modules.business.service.dto.FileReviewDTO;

import java.util.List;


/**
* @description 文件评审Service
* <AUTHOR>
* @date 2025-08-20
**/
public interface FileReviewService {
    
    /**
	 * 分页查找查找 文件评审
	 * 
	 * @param dto 文件评审dto实体对象
	 * @return 
	 */
	TorchResponse<List<FileReviewVO>> findFileReviewPage(FileReviewDTO dto);

    /**
	 * 添加 \修改 文件评审
	 * 
	 * @param fileReviewDto 文件评审dto实体对象
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse saveOrUpdate(FileReviewDTO fileReviewDto);
	
	/**
	 * 通过id查找文件评审
	 *
	 * @param id 主键
	 * @return 
	 */
	TorchResponse<FileReviewVO> findFileReview(String id);
	
	/**
	 * 删除 文件评审
	 * 
	 * @param ids 主键集合  
	 * @return 
	 */
	@SuppressWarnings("rawtypes")
	TorchResponse delete(String[] ids);

	/**
	 * 查找关联信息 文件评审
	 *
	 * @param id 关键信息列ID
	 * @return
	 */
	TorchResponse<List<FileReviewVO>> getOptionsList(String id);




    /**
     * 根据条件查询文件评审列表
     *
     * @param dto 文件评审信息
     * @return 文件评审集合信息
     */
    List<FileReviewVO> selectFileReviewList(FileReviewDTO dto);

    /**
     * 导入文件评审数据
     *
     * @param fileReviewList 文件评审数据列表
     * @param unionColumns 作为确认数据唯一性的字段集合
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    TorchResponse importFileReview(List<FileReviewVO> fileReviewList, List<String> unionColumns, Boolean isUpdateSupport, String operName);

	/**
	 * 根据IDS获取文件评审数据
	 * @param ids
	 * @return
	 */
	TorchResponse selectFileReviewListByIds(List<String> ids);

	/**
	 * 产品列表下发生成文件评审
	 * @param orders
	 * @return
	 */
	TorchResponse issueProductCreateFileVeiw(List<ProductionOrder> orders);

	/**
	 * 评审上传变更
	 * @param fileReviewDto
	 * @return
	 */
	TorchResponse reviewUpdateFileReview(FileReviewDTO fileReviewDto);
}