package com.huatek.frame.modules.business.rest;

import com.huatek.frame.common.utils.Constant;
import com.huatek.frame.modules.business.service.dto.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.huatek.frame.common.annotation.Log;
import com.huatek.frame.common.annotation.perm.TorchPerm;
import com.huatek.frame.common.utils.poi.ExcelUtil;
import com.huatek.frame.modules.business.domain.SawOrder;
import com.huatek.frame.modules.business.service.SawOrderService;
import com.huatek.frame.modules.business.domain.vo.SawOrderVO;
import com.huatek.frame.common.response.TorchResponse;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import java.util.List;
import java.io.IOException;
import javax.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;


import java.util.ArrayList;
import java.util.Map;

/**
* <AUTHOR>
* @date 2025-08-20
**/
@Api(tags = "监制验收工单管理")
@RestController
@RequestMapping("/api/sawOrder")
public class SawOrderController {

	@Autowired
    private SawOrderService sawOrderService;

	/**
	 * 监制验收工单列表
	 * 
	 * @param dto 监制验收工单DTO 实体对象
	 * @return
	 */
    @Log("分页查询监制验收工单列表")
    @ApiOperation(value = "监制验收工单列表查询")
    @PostMapping(value = "/sawOrderList", produces = { "application/json;charset=utf-8" })
    @TorchPerm("sawOrder:list")
    public TorchResponse<List<SawOrderVO>> query(@RequestBody SawOrderPageDTO requestParam){
        return sawOrderService.findSawOrderPage(requestParam);
    }

	/**
	 * 新增/修改监制验收工单
	 * 
	 * @param requestParam 新增/修改监制验收工单DTO实体对象
	 * @return
	 * @throws Exception 
	 */
    @SuppressWarnings("rawtypes")
    @Log("新增/修改监制验收工单")
    @ApiOperation(value = "监制验收工单新增/修改操作")
    @PostMapping(value = "/addOrUpdateSawOrder", produces = { "application/json;charset=utf-8" })
    public TorchResponse add(@RequestBody SawOrderAddOrUpdateDTO requestParam) throws Exception {
		return sawOrderService.saveOrUpdate(requestParam);
	}

	/**
	 * 查询监制验收工单详情
	 * 
	 * @param id 主键id
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
    @Log("监制验收工单详情")
    @ApiOperation(value = "监制验收工单详情查询")
    @GetMapping(value = "/detail/{id}", produces = { "application/json;charset=utf-8" })
    @TorchPerm("sawOrder:detail")
	public TorchResponse detail(@PathVariable(value = "id") String id) {
		return sawOrderService.findSawOrder(id);
	}

	/**
	 * 删除监制验收工单
	 * 
	 * @param ids
	 * @return
	 */
	@SuppressWarnings("rawtypes")
    @Log("删除监制验收工单")
    @ApiOperation(value = "监制验收工单删除操作")
    @TorchPerm("sawOrder:del")
    @PostMapping(value = "/delete", produces = { "application/json;charset=utf-8" })
	public TorchResponse delete(@RequestBody List<String> ids) {
		return sawOrderService.delete(ids);
	}

    @ApiOperation(value = "监制验收工单联动选项值查询")
    @GetMapping(value = "/optionsList/{id}", produces = { "application/json;charset=utf-8" })
	public TorchResponse getOptionsList(@PathVariable(value = "id") String id) {
		return sawOrderService.getOptionsList(id);
	}





    @Log("监制验收工单导出")
    @ApiOperation(value = "监制验收工单导出")
    @TorchPerm("sawOrder:export")
    @PostMapping("/export")
    public void export(HttpServletResponse response, @RequestBody SawOrderDTO dto)
    {
        List<SawOrderVO> list = sawOrderService.selectSawOrderList(dto);
        ExcelUtil<SawOrderVO> util = new ExcelUtil<SawOrderVO>(SawOrderVO.class);
        util.exportExcel(response, list, "监制验收工单数据");
    }

    @Log("监制验收工单导入")
    @ApiOperation(value = "监制验收工单导入")
    @TorchPerm("sawOrder:import")
    @PostMapping("/importData")
    public TorchResponse importData(@RequestParam("file") MultipartFile file, @RequestParam("unionColumns") List<String> unionColumns) throws Exception
    {
        ExcelUtil<SawOrderVO> util = new ExcelUtil<SawOrderVO>(SawOrderVO.class);
        List<SawOrderVO> list = util.importExcel(file.getInputStream());
        return sawOrderService.importSawOrder(list, unionColumns, true, "");
    }

    @Log("监制验收工单导入模板")
    @ApiOperation(value = "监制验收工单导入模板下载")
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response) throws IOException
    {
        ExcelUtil<SawOrderVO> util = new ExcelUtil<SawOrderVO>(SawOrderVO.class);
        util.importTemplateExcel(response, "监制验收工单数据");
    }

    @Log("根据Ids获取监制验收工单列表")
    @ApiOperation(value = "监制验收工单 根据Ids批量查询")
    @PostMapping(value = "/sawOrderList/ids", produces = {"application/json;charset=utf-8"})
    public TorchResponse getSawOrderListByIds(@RequestBody List<String> ids) {
        return sawOrderService.selectSawOrderListByIds(ids);
    }

    /**
     * 监制验收工单主子表单组合提交
     *
	 * @param sawOrderDto 监制验收工单DTO实体对象
     * @return
     */
    @SuppressWarnings("rawtypes")
    @Log("监制验收工单主子表单组合提交")
    @ApiOperation(value = "监制验收工单主子表单组合提交")
    //@TorchPerm("sawOrder:masterDetailSubmit")
    @PostMapping(value = "/masterDetailSubmit", produces = {"application/json;charset=utf-8"})
    public TorchResponse submitMasterDetails(@RequestBody SawOrderDTO sawOrderDto) {
        return sawOrderService.submitMasterDetails(sawOrderDto);
    }

    /**
     * 分页查询某个监制验收工单的产品明细
     * @param requestParam
     * @return
     */
    @Log("分页查询某个监制验收工单的产品明细")
    @ApiOperation(value = "分页查询某个监制验收工单的产品明细")
    @PostMapping(value = "/productPage", produces = {"application/json;charset=utf-8"})
    public TorchResponse pageProduct(@RequestBody SawOrderProductPageDTO requestParam){
        return sawOrderService.pageProduct(requestParam);
    }

    /**
     * 批量完成产品
     * @param requestParam
     * @return
     */
    @Log("批量完成产品")
    @ApiOperation(value = "批量完成产品")
    @PostMapping(value = "/productFinish")
    public TorchResponse finishProduct(@RequestBody SawOrderProductFinishDTO requestParam){
        return sawOrderService.finishProduct(requestParam);
    }

    /**
     * 批量完成监制验收工单
     * @param requestParam
     * @return
     */
    @Log("批量完成监制验收工单")
    @ApiOperation(value = "批量完成监制验收工单")
    @PostMapping(value = "/sawOrderFinish")
    public TorchResponse finishSawOrder(@RequestBody SawOrderFinishDTO requestParam){
        return sawOrderService.finishSawOrder(requestParam);
    }



}