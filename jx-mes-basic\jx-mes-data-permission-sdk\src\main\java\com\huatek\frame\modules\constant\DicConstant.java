package com.huatek.frame.modules.constant;

public class DicConstant  {

    public class CommonDic{
        public static final String DIC_YES = "0";//是
        public static final String DIC_NO = "1";//否
        public static final String DEFAULT_TRUE="true";
        public static final String DEFAULT_FALSE="false";
        public static final String DEFAULT_ZERO="0";
        public static final String DEFAULT_ONE="1";

        /**
         * 未删除
         */
        public static final String UN_DELETED = "0";

        /**
         * 已删除
         */
        public static final String DELETED = "1";

        public static final String DRAFT="0";//草稿

        public static final String WAITAPPROVE="1"; //待审批
        public static final String APPROVED="2";//审批通过
        public static final String REJECT="3";//驳回

    }

    /**
     * 消息管理字典
     */
    public class MessageManagement{
        /**
         * 消息状态
         */

        /**
         * 未读
         */
        public static final String MESSAGE_MANAGEMENT_STATUS_HAS_READ = "0";

        /**
         * 已读
         */
        public static final String MESSAGE_MANAGEMENT_STATUS_UN_READ = "1";
    }

    public class ProductionOrder{

        /**
         * 订单类型
         */

        public static final String WORK_ORDER_TYPE_RELIABILITY = "K";//可靠性类型工单：DPA、专项分析、失效分析、鉴定试验、质量一致性、其它试验类型
        public static final String WORK_ORDER_TYPE_SCREENING = "S";//测筛类型工单：一筛、二筛、环境试验、复验类型、微波鉴定试验

        public static final String WORK_ORDER_TYPE_SAW = "SAW"; //监制验收



        /**
         * 订单状态
         */
        public static final String WORK_ORDER_STATUS_DRAFT="0";//草稿(待提交）
        public static final String WORK_ORDER_STATUS_WAITAPPROVE="1";//待审批
        public static final String WORK_ORDER_STATUS_APPROVED="2";//审批通过
        public static final String WORK_ORDER_STATUS_REJECT="3";//驳回
        public static final String WORK_ORDER_STATUS_PROGRESS="4";//进行中
        public static final String WORK_ORDER_STATUS_PAUSE="5";//暂停
        public static final String WORK_ORDER_STATUS_CANCEL="6";//取消
        public static final String WORK_ORDER_STATUS_COMPLETE="7";//完成
        public static final String WORK_ORDER_STATUS_OUTSOURCED="8";//已外协
        public static final String WORK_ORDER_STATUS_PREPARATION="9";//待制单
        /**
         * 生产阶段
         */
        public static final String PRODUCTION_STAGE_NOSTARTED="0";//未开始
        public static final String PRODUCTION_STAGE_PRODUCTION="1";//生产中
        public static final String PRODUCTION_STAGE_INSTORE="2";//入库
        public static final String PRODUCTION_STAGE_OUTSTORE="3";//出库
        /**
         * 试验类型
         */

        public static final String TEST_TYPE_ONE="14";//一筛
        public static final String TEST_TYPE_TWO="13";//二筛
        public static final String TEST_TYPE_ENVIRONMENT="12";//环境
        public static final String TEST_TYPE_REINSPECTION="11";//复验
        public static final String TEST_TYPE_DPA="10";//DPA
        public static final String TEST_TYPE_SPECIAL_ANALYSIS="9";//专项分析
        public static final String TEST_TYPE_FAILURE_ANALYSIS="8";//失效分析
        public static final String TEST_TYPE_QUALIFICATION_TEST="7";//鉴定试验
        public static final String TEST_TYPE_MICROWAVE_QUALIFICATION_TEST="6";//微波鉴定试验
        public static final String TEST_TYPE_QUALITY_CONSISTENCY="5";//质量一致性
        public static final String TEST_TYPE_OTHER_TEST="4";//其他试验
        public static final String TEST_TYPE_EXECUTIVE_PRODUCER="3";//监制
        public static final String TEST_TYPE_CHECK_ACCEPT="2";//验收
        public static final String TEST_TYPE_CERTIFICATION="1";//出证
        /**
         * 试验方式
         */
        public static final String TEST_METHODLOGY_SELF="1";//自产
        public static final String TEST_METHODLOGY_OUTSORCEING="2";//外协
        public static final String TEST_METHODLOGY_CANNOT_SCREENED="3";//不可筛
        /**
         * 复制类型
         */
        public static final String COPY_TYPE_SAME="1";//同级复制
        public static final String COPY_TYPE_CHILD="2";//子级复制

        //==========监制验收工单==================

        /**
         * 验收类型-厂家代验
         */
        public static final String SUPERVISOR_ACCEPTANCE_TYPE_FACTORY_ACCEPT = "0";

        /**
         * 验收类型-下厂验收
         */
        public static final String SUPERVISOR_ACCEPTANCE_TYPE_ONSITE_ACCEPT  = "1";

        /**
         * 状态-未完成
         */
        public static final String SUPERVISOR_ACCEPTANCE_STATUS_UNFINISHED = "0";

        /**
         * 状态-完成
         */
        public static final String SUPERVISOR_ACCEPTANCE_STATUS_FINISHED = "1";



        //==============外协=================

        /**
         * 外协状态-草稿
         */
        public static final String OUTSOURCING_STATUS_DRAFT = "0";

        /**
         * 外协状态-待审批
         */
        public static final String OUTSOURCING_STATUS_PENDING_APPROVAL = "1";

        /**
         * 外协状态-审批通过
         */
        public static final String OUTSOURCING_STATUS_APPROVALED = "2";

        /**
         * 外协状态-审批驳回
         */
        public static final String OUTSOURCING_STATUS_REJECTED = "3";

        /**
         * 外协状态-已验收
         */
        public static final String OUTSOURCING_STATUS_ACCEPTED = "4";

        //==========外协类型================
        /**
         * 整单外协
         */
        public static final String OUTSOURCING_TYPE_ENTIRE = "0";

        /**
         * 工序外协
         */
        public static final String OUTSOURCING_TYPE_PROCESS = "1";

       
        
        //生产任务状态
        public static final String PRODUCTION_TASK_STATUS_WEIKAISHI  = "0";
        public static final String PRODUCTION_TASK_STATUS_JINXINGZHONG  = "1";
        public static final String PRODUCTION_TASK_STATUS_DAIAPPROVE  = "2";
        public static final String PRODUCTION_TASK_STATUS_BOHUI  = "3";
        public static final String PRODUCTION_TASK_STATUS_ZANTING  = "4";
        public static final String PRODUCTION_TASK_STATUS_QUXIAO  = "5";
        public static final String PRODUCTION_TASK_STATUS_WANCHENG = "6";
        public static final String PRODUCTION_TASK_STATUS_YIWAIXIE = "7";




        //生产任务暂停原因
        public static final String PRODUCTION_PAUSE_REASON_ZHENGCHANG = "0";
        public static final String PRODUCTION_PAUSE_REASON_YICHANG = "1";
        public static final String PRODUCTION_PAUSE_REASON_WAIXIE = "2";

        
        /**
         * 操作类型常量
         */
        public static final String OPERATION_TYPE_START = "0";      // 开始
        public static final String OPERATION_TYPE_PAUSE = "1";      // 暂停
        public static final String OPERATION_TYPE_RESUME = "2";    // 恢复
        public static final String OPERATION_TYPE_CANCEL = "3";    // 取消
        public static final String OPERATION_TYPE_SUBMIT = "4";      // 提交
        public static final String OPERATION_TYPE_BOHUI = "5";    // 审批驳回
        public static final String OPERATION_TYPE_WANCHENG = "6";    // 完成


    }

    public class OutSourcingStatus{
        public static final String OUTSOURCING_DRAFT = "0";//草稿
        public static final String OUTSOURCING_WAITAPPROVE = "1";//待审批
        public static final String OUTSOURCING_APPROVED="2";//审批通过
        public static final String OUTSOURCING_REJECT="3";//驳回
        public static final String OUTSOURCING_ACCEPTED="4";//已验收
    }
    /**
     * 销售管理
     */
    public class SalesOrder{
        //==============封裝訂單===================
        /**
         * 进行中
         */
        public static final String PACKAGE_ORDER_STATUS_IN_PROGRESS = "0";

        /**
         * 已完成
         */
        public static final String PACKAGE_ORDER_STATUS_FINISHED = "1";



        //================产品列表=======================
        /**
         * 未下发
         */
        public static final String PRODUCT_LIST_STATUS_PENDING = "0";

        /**
         * 已下发
         */
        public static final String PRODUCT_LIST_STATUS_COMPLETED = "1";

        /**
         * 已退回
         */
        public static final String PRODUCT_LIST_STATUS_REJECTED = "2";

        /**
         * 不可筛
         */
        public static final String PRODUCT_LIST_STATUS_LOCKED = "3";

        /**
         * 监制验收状态完成
         */
        public static final String PRODUCT_LIST_STATUS_FINISHED = "4";

        /**
         * 监制验收状态未完成
         */
        public static final String PRODUCT_LIST_STATUS_UNFINISHED = "5";

        //========测评订单==========

        /**
         * 监制验收产品状态-未完成
         */
        public static final String SAW_ORDER_PRODUCT_LIST_STATUS_UNFINISHED = "0";

        /**
         * 监制验收产品状态-完成
         */
        public static final String SAW_ORDER_PRODUCT_LIST_STATUS_FINISHED = "1";

        /**
         * 未开始
         */
        public static final String EVALUATION_ORDER_STATUS_NOT_STARTED = "0";

        /**
         * 生产中
         */
        public static final String EVALUATION_ORDER_STATUS_IN_PRODUCTION = "1";

        /**
         * 入库
         */
        public static final String EVALUATION_ORDER_STATUS_IN_STOCK = "2";

        /**
         * 出库
         */
        public static final String EVALUATION_ORDER_STATUS_OUT_STOCK = "3";

        //===============能力核验====================

        /**
         * 具备
         */
        public static final String CAPABILITY_VERIFICATION_RESULT_TRUE = "0";

        /**
         * 不具备
         */
        public static final String CAPABILITY_VERIFICATION_RESULT_FALSE = "1";

        /**
         * 草稿
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_DRAFT = "0";

        /**
         * 已核验
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_VERIFIED = "1";

        /**
         * 待确认
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_PENDING_CONFIRMATION = "2";

        /**
         * 转开发
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_TRANSFERRED_TO_DEV = "3";

        /**
         * 已确认
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_CONFIRMED = "4";


        /**
         * 已对账
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_STATUS_YES="1";
        /**
         * 未对账
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_STATUS_NO="0";


        /**
         * 客户
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_STATUS_KEHU="1";
        /**
         * 君信
         */
        public  static final String PRODUCTION_VALUE_CALCULATION_TYPE_JUNXIN="0";

    }


    public class Group{
        public static final String GROUP_KEKAOXING="H01001002";//可靠性部
        public static final String GROUP_SHENGCHAN="H01001001";//生产部
    }
    public class Role{
        public static final String ROLE_DIAODU="调度";
        public static final String ROLE_SHENGCHENZHUGUAN="生产部主管";
    }
    //设备管理
    public class deviceManagement{
        /**
         * 已完成
         */
        public static final String DEVICE_MANAGEMENT_STATUS_FINISHED ="1";
        /**
         * 已过期
         */
        public static final  String DEVICE_MANAGEMENT_STATUS_EXPIRED = "2";
        /**
         * 未开始
         */
        public static final String DEVICE_MANAGEMENT_STATUS_UN_FINISHED = "3";
    }
    /**
     * 技术管理
     */
    public class TechnicalManagement {

        //================产品列表=======================
        /**
         * 进行中
         */
        public static final String CAPABILITY_VERIFICATION_STATUS_WEIKAISHI = "0";
        public static final String CAPABILITY_VERIFICATION_STATUS_JINXINGZHONG = "1";
        public static final String CAPABILITY_VERIFICATION_STATUS_QUANBUWANCHENG = "2";
        public static final String CAPABILITY_VERIFICATION_STATUS_YICHANGGUANBI = "3";
        public static final String CAPABILITY_VERIFICATION_STATUS_BUFENWANCHENG = "4";

        public static final String CAPABILITY_VERIFICATION_COMPLETED_BUFENWANCHENG = "0";
        public static final String CAPABILITY_VERIFICATION_COMPLETED_QUANBUWANCHENG = "1";
        public static final String CAPABILITY_VERIFICATION_COMPLETED_YICHANGGUANBI = "2";

        /**
         * 是否具备 字典值
         */
        public static final String CAPABILITY_VERIFICATION_VERIFICATIONRESULT_YES = "0";
        public static final String CAPABILITY_VERIFICATION_VERIFICATIONRESULT_NO = "1";

        public static final String CAPABILITY_REVIEW_ZIXUN = "0";
        public static final String CAPABILITY_REVIEW_DINGDAN = "1";

        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_CESHI = "0"; //检验类型-测试
        public static final String CAPABILITY_REVIEW_INSPECTIONTYPE_LAOHUA = "1"; //检验类型-老化



        //反馈/处理
        public static final String FEEDBACKPROCESSING_FANKUIQUERENKAIFA = "0"; //反馈确认开发
        public static final String FEEDBACKPROCESSING_BUKESHAI = "1"; //不可筛
        public static final String FEEDBACKPROCESSING_DAIZHUANKAIFA = "2"; //待转开发
        public static final String FEEDBACKPROCESSING_WUXUHEYAN = "3"; //无需核验
        public static final String FEEDBACKPROCESSING_YIJUBEINENGLI = "4"; //已具备能力
        public static final String FEEDBACKPROCESSING_YIZHUANKAIFA = "5"; //已转开发

        //评审结果
        public static final String CAPABILITY_REVIEW_RESULT_NO = "0";
        public static final String CAPABILITY_REVIEW_RESULT_YES = "1";

        //===============能力资产=================

        public static final String CAPABILITY_ASSET_CAPABILITY_TYPE_CESHI = "0"; //能力类型-测试
        public static final String CAPABILITY_ASSET_CAPABILITY_TYPE_LAOHUA = "1"; //能力类型-老化
    }



}
