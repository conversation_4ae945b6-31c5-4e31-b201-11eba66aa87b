package com.huatek.frame.modules.business.domain.vo;

import com.huatek.frame.common.annotation.poi.Excel;
import com.huatek.frame.common.utils.HuatekTools;
import com.huatek.frame.modules.conf.CustomerBigDecimalSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.sql.Timestamp;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import java.sql.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.math.BigDecimal;
import java.io.Serializable;

/**
* @description 工时报表VO实体类
* <AUTHOR>
* @date 2025-08-18
**/
@Data
@ApiModel("工时报表DTO实体类")
public class WorkReportTableVO implements Serializable {

	private static final long serialVersionUID = 1L;

    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;

    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    @Excel(name = "工单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String workOrderNumber;

    /**
	 * 送检数量
     **/
    @ApiModelProperty("送检数量")
    @Excel(name = "送检数量",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long inspectionQuantity2;

    /**
	 * 标准工序名称
     **/
    @ApiModelProperty("标准工序名称")
    @Excel(name = "标准工序名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String standardProcessName;

    /**
	 * 客户试验名称
     **/
    @ApiModelProperty("客户试验名称")
    @Excel(name = "客户试验名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String customerTestName;

    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    @Excel(name = "产品名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productName;

    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    @Excel(name = "产品型号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productModel;

    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    @Excel(name = "生产厂家",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String manufacturer;

    /**
	 * 批次号
     **/
    @ApiModelProperty("批次号")
    @Excel(name = "批次号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String batchNumber;

    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    @Excel(name = "产品分类",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productCategory;

    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    @Excel(name = "产品资料",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String productInformation1;

    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    @Excel(name = "委托单位",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String entrustedUnit;

    /**
	 * 试验方式
     **/
    @ApiModelProperty("试验方式")
    @Excel(name = "试验方式",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String testMethodology;

    /**
	 * 工序开始时间
     **/
    @ApiModelProperty("工序开始时间")
    @Excel(name = "工序开始时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp processStartTime;

    /**
	 * 工序结束时间
     **/
    @ApiModelProperty("工序结束时间")
    @Excel(name = "工序结束时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp processEndTime;

    /**
	 * 工序时长
     **/
    @ApiModelProperty("工序时长")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "工序时长",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal processDuration;

    /**
	 * 报工人
     **/
    @ApiModelProperty("报工人")
    @Excel(name = "报工人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String reporter4;

    /**
	 * 研发任务编号
     **/
    @ApiModelProperty("研发任务编号")
    @Excel(name = "研发任务编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String rdTaskNumber;

    /**
	 * 是否参与核算
     **/
    @ApiModelProperty("是否参与核算")
    @Excel(name = "是否参与核算",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String wipaia;

    /**
	 * 是否外协工序
     **/
    @ApiModelProperty("是否外协工序")
    @Excel(name = "是否外协工序",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String whetherOutsourcedProcess6;

    /**
	 * 设备共用工单
     **/
    @ApiModelProperty("设备共用工单")
    @Excel(name = "设备共用工单",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String sharedEquipmentWorkOrder;

    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    @Excel(name = "订单编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String orderNumber;

    /**
	 * 样本总数
     **/
    @ApiModelProperty("样本总数")
    @Excel(name = "样本总数",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long sampleTotalCount;

    /**
	 * 设备编号
     **/
    @ApiModelProperty("设备编号")
    @Excel(name = "设备编号",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String deviceSerialNumber;

    /**
	 * 设备名称
     **/
    @ApiModelProperty("设备名称")
    @Excel(name = "设备名称",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String deviceName;

    /**
	 * 固定资产编码
     **/
    @ApiModelProperty("固定资产编码")
    @Excel(name = "固定资产编码",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String fixedAssetCoding;

    /**
	 * 同工序时长
     **/
    @ApiModelProperty("同工序时长")
    @Excel(name = "同工序时长",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private String durationOfTheSameProcess;

    /**
	 * 设备开始时间
     **/
    @ApiModelProperty("设备开始时间")
    @Excel(name = "设备开始时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp equipmentEndTime;

    /**
	 * 设备结束时间
     **/
    @ApiModelProperty("设备结束时间")
    @Excel(name = "设备结束时间",
        cellType = Excel.ColumnType.NUMERIC,
        dateFormat = "yyyy-MM-dd HH:mm:ss",
        type = Excel.Type.ALL)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp equipmentEndTime27;

    /**
	 * 设备用时（h）
     **/
    @ApiModelProperty("设备用时（h）")
    @JsonSerialize(using = CustomerBigDecimalSerialize.class)
    @Excel(name = "设备用时（h）",
        cellType = Excel.ColumnType.NUMERIC,
        type = Excel.Type.ALL)
    private BigDecimal equipmentRunningTimeh;

    /**
	 * 设备能耗
     **/
    @ApiModelProperty("设备能耗")
    @Excel(name = "设备能耗",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long equipmentEnergyConsumption;

    /**
	 * 设备功耗
     **/
    @ApiModelProperty("设备功耗")
    @Excel(name = "设备功耗",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.ALL)
    private Long equipmentPowerConsumption;

    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;

    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    @Excel(name = "更新人",
        cellType = Excel.ColumnType.STRING,
        type = Excel.Type.EXPORT)
    private String codexTorchUpdater;

    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;

    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    private Timestamp codexTorchCreateDatetime;

    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    private Timestamp codexTorchUpdateDatetime;

    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;


}