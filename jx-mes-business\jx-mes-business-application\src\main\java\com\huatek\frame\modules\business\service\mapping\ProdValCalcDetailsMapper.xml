<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.huatek.frame.modules.business.mapper.ProdValCalcDetailsMapper">
	<sql id="Base_Column_List">
		t.id as id,
		t.production_value_calculation_id as productionValueCalculationId,
		t.experiment_project as experimentProject,
		t.quantity as quantity,
		t.unit_price as unitPrice,
		t.total as total,
		t.codex_torch_creator_id as codexTorchCreatorId,
		t.codex_torch_updater as codexTorchUpdater,
		t.codex_torch_group_id as codexTorchGroupId,
		t.codex_torch_create_datetime as codexTorchCreateDatetime,
		t.codex_torch_update_datetime as codexTorchUpdateDatetime,
		t.codex_torch_deleted as codexTorchDeleted
	</sql>
	<select id="selectProdValCalcDetailsPage" parameterType="com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO">
		select
		<include refid="Base_Column_List" />
			from prod_val_calc_details t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="productionValueCalculationId != null and productionValueCalculationId != ''">
                    and t.production_value_calculation_id  like concat('%', #{productionValueCalculationId} ,'%')
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  like concat('%', #{experimentProject} ,'%')
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>
                <if test="unitPrice != null and unitPrice != ''">
                    and t.unit_price  = #{unitPrice}
                </if>
                <if test="total != null and total != ''">
                    and t.total  = #{total}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProdValCalcDetailsList" parameterType="com.huatek.frame.modules.business.service.dto.ProdValCalcDetailsDTO"
		resultType="com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO">
		select
		<include refid="Base_Column_List" />
			from prod_val_calc_details t
            <where>
                and t.codex_torch_deleted = '0'
                <if test="productionValueCalculationId != null and productionValueCalculationId != ''">
                    and t.production_value_calculation_id  like concat('%', #{productionValueCalculationId} ,'%')
                </if>
                <if test="experimentProject != null and experimentProject != ''">
                    and t.experiment_project  like concat('%', #{experimentProject} ,'%')
                </if>
                <if test="quantity != null and quantity != ''">
                    and t.quantity  = #{quantity}
                </if>
                <if test="unitPrice != null and unitPrice != ''">
                    and t.unit_price  = #{unitPrice}
                </if>
                <if test="total != null and total != ''">
                    and t.total  = #{total}
                </if>
                <if test="codexTorchCreatorId != null and codexTorchCreatorId != ''">
                    and t.codex_torch_creator_id  like concat('%', #{codexTorchCreatorId} ,'%')
                </if>
                <if test="codexTorchUpdater != null and codexTorchUpdater != ''">
                    and t.codex_torch_updater  like concat('%', #{codexTorchUpdater} ,'%')
                </if>
                <if test="codexTorchGroupId != null and codexTorchGroupId != ''">
                    and t.codex_torch_group_id  like concat('%', #{codexTorchGroupId} ,'%')
                </if>
                <if test="codexTorchCreateDatetime != null">
                    and t.codex_torch_create_datetime  = #{codexTorchCreateDatetime}
                </if>
                <if test="codexTorchUpdateDatetime != null">
                    and t.codex_torch_update_datetime  = #{codexTorchUpdateDatetime}
                </if>
                <if test="codexTorchDeleted != null and codexTorchDeleted != ''">
                    and t.codex_torch_deleted  like concat('%', #{codexTorchDeleted} ,'%')
                </if>
                ${params.dataScope}
            </where>
	</select>

    <select id="selectProdValCalcDetailsListByIds"
		resultType="com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO">
		select
		<include refid="Base_Column_List" />
			from prod_val_calc_details t
            <where>
                <if test="ids != null and ids.size > 0" >
                t.id in
                    <foreach collection="ids" close=")" open="(" separator="," index="" item="id">
#{id}                    </foreach>
                </if>
            </where>
	</select>
    
    <select id="selectProdValCalcDetailsListByProductionValueCalculationId"
		resultType="com.huatek.frame.modules.business.domain.vo.ProdValCalcDetailsVO">
		select
		<include refid="Base_Column_List" />
			from prod_val_calc_details t
            <where>
                <if test="productionValueCalculationId != null and productionValueCalculationId != ''">
                    and t.production_value_calculation_id = #{productionValueCalculationId}
                </if>
            </where>
	</select>
</mapper>