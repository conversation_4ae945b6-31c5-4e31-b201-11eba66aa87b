2025-08-22 13:12:10,193 INFO gateway [main] com.huatek.frame.GatewayApplication [SpringApplication.java : 655] The following profiles are active: dev
2025-08-22 13:12:11,955 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 249] Multiple Spring Data modules found, entering strict repository configuration mode!
2025-08-22 13:12:11,964 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 127] Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-22 13:12:12,048 INFO gateway [main] o.s.d.r.c.RepositoryConfigurationDelegate [RepositoryConfigurationDelegate.java : 187] Finished Spring Data repository scanning in 30ms. Found 0 Redis repository interfaces.
2025-08-22 13:12:12,614 INFO gateway [main] o.s.cloud.context.scope.GenericScope [GenericScope.java : 295] BeanFactory id=64f4209a-d1ff-3528-9ea5-7ff5c377b0aa
2025-08-22 13:12:12,856 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'redisConfig' of type [com.huatek.frame.gate.conf.RedisConfig$$EnhancerBySpringCGLIB$$fd720817] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-22 13:12:13,621 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'spring.cloud.sentinel-com.alibaba.cloud.sentinel.SentinelProperties' of type [com.alibaba.cloud.sentinel.SentinelProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-22 13:12:13,753 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration' of type [com.alibaba.cloud.sentinel.custom.SentinelAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-22 13:12:13,766 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-22 13:12:13,770 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactiveLoadBalancerConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-22 13:12:13,778 INFO gateway [main] o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker [PostProcessorRegistrationDelegate.java : 335] Bean 'deferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-22 13:12:15,349 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-22 13:12:15,370 INFO gateway [main] c.n.c.sources.URLConfigurationSource [URLConfigurationSource.java : 122] To enable URLs as dynamic configuration sources, define System property archaius.configurationSource.additionalUrls or make config.properties available on classpath.
2025-08-22 13:12:18,080 INFO gateway [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-22 13:12:20,238 INFO gateway [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-22 13:12:22,225 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [After]
2025-08-22 13:12:22,226 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Before]
2025-08-22 13:12:22,227 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Between]
2025-08-22 13:12:22,227 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Cookie]
2025-08-22 13:12:22,227 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Header]
2025-08-22 13:12:22,228 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Host]
2025-08-22 13:12:22,228 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Method]
2025-08-22 13:12:22,228 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Path]
2025-08-22 13:12:22,229 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Query]
2025-08-22 13:12:22,229 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [ReadBody]
2025-08-22 13:12:22,229 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [RemoteAddr]
2025-08-22 13:12:22,229 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [Weight]
2025-08-22 13:12:22,230 INFO gateway [main] o.s.c.g.r.RouteDefinitionRouteLocator [RouteDefinitionRouteLocator.java : 139] Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-22 13:12:22,744 INFO gateway [main] c.a.c.s.SentinelWebFluxAutoConfiguration [SentinelWebFluxAutoConfiguration.java : 91] [Sentinel Starter] register Sentinel SentinelWebFluxFilter
2025-08-22 13:12:22,829 INFO gateway [main] o.s.s.c.ThreadPoolTaskScheduler [ExecutorConfigurationSupport.java : 181] Initializing ExecutorService 'Nacos-Watch-Task-Scheduler'
2025-08-22 13:12:25,192 INFO gateway [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-22 13:12:28,029 INFO gateway [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-22 13:12:30,931 INFO gateway [main] o.s.b.w.e.netty.NettyWebServer [NettyWebServer.java : 109] Netty started on port(s): 8881
2025-08-22 13:12:33,509 INFO gateway [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-22 13:12:36,403 INFO gateway [main] o.s.cloud.commons.util.InetUtils [InetUtils.java : 170] Cannot determine local hostname
2025-08-22 13:12:37,519 INFO gateway [main] c.a.c.n.r.NacosServiceRegistry [NacosServiceRegistry.java : 75] nacos registry, jx-mes jx-mes-gateway ************:8881 register finished
2025-08-22 13:12:37,625 INFO gateway [main] com.huatek.frame.GatewayApplication [StartupInfoLogger.java : 61] Started GatewayApplication in 40.071 seconds (JVM running for 41.68)
2025-08-22 13:12:37,640 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway+jx-mes
2025-08-22 13:12:37,643 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway, group=jx-mes, cnt=1
2025-08-22 13:12:37,646 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway-dev.yml+jx-mes
2025-08-22 13:12:37,646 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway-dev.yml, group=jx-mes, cnt=1
2025-08-22 13:12:37,647 INFO gateway [main] c.a.n.c.config.impl.ClientWorker [ClientWorker.java : 169] [fixed-127.0.0.1_8848] [subscribe] jx-mes-gateway.yml+jx-mes
2025-08-22 13:12:37,647 INFO gateway [main] c.a.n.client.config.impl.CacheData [CacheData.java : 92] [fixed-127.0.0.1_8848] [add-listener] ok, tenant=, dataId=jx-mes-gateway.yml, group=jx-mes, cnt=1
2025-08-22 13:28:40,413 INFO gateway [reactor-http-nio-2] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-22 13:28:40,458 INFO gateway [reactor-http-nio-2] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 197] Client: jx-mes-basic-application instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-basic-application,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-08-22 13:28:40,474 INFO gateway [reactor-http-nio-2] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 222] Using serverListUpdater PollingServerListUpdater
2025-08-22 13:28:40,510 INFO gateway [reactor-http-nio-2] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-22 13:28:40,516 INFO gateway [reactor-http-nio-2] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 150] DynamicServerListLoadBalancer for client jx-mes-basic-application initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-basic-application,current list of Servers=[************:8882],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:8882;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@26f1ed08
2025-08-22 13:28:40,581 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 13:28:40,581 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-22 13:28:40,582 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-22 13:28:41,483 INFO gateway [PollingServerListUpdater-0] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-basic-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-22 13:28:42,865 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 13:29:20,794 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionTask
2025-08-22 13:29:21,007 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_status
2025-08-22 13:29:21,518 INFO gateway [reactor-http-nio-3] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-22 13:29:21,543 INFO gateway [reactor-http-nio-3] c.n.loadbalancer.BaseLoadBalancer [BaseLoadBalancer.java : 197] Client: jx-mes-business-application instantiated a LoadBalancer: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-business-application,current list of Servers=[],Load balancer stats=Zone stats: {},Server stats: []}ServerList:null
2025-08-22 13:29:21,554 INFO gateway [reactor-http-nio-3] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 222] Using serverListUpdater PollingServerListUpdater
2025-08-22 13:29:21,575 INFO gateway [reactor-http-nio-3] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-22 13:29:21,578 INFO gateway [reactor-http-nio-3] c.n.l.DynamicServerListLoadBalancer [DynamicServerListLoadBalancer.java : 150] DynamicServerListLoadBalancer for client jx-mes-business-application initialized: DynamicServerListLoadBalancer:{NFLoadBalancer:name=jx-mes-business-application,current list of Servers=[************:8886],Load balancer stats=Zone stats: {unknown=[Zone:unknown;	Instance count:1;	Active connections count: 0;	Circuit breaker tripped count: 0;	Active connections per server: 0.0;]
},Server stats: [[Server:************:8886;	Zone:UNKNOWN;	Total Requests:0;	Successive connection failure:0;	Total blackout seconds:0;	Last connection made:Thu Jan 01 08:00:00 CST 1970;	First connection made: Thu Jan 01 08:00:00 CST 1970;	Active Connections:0;	total failure count in last (1000) msecs:0;	average resp time:0.0;	90 percentile resp time:0.0;	95 percentile resp time:0.0;	min resp time:0.0;	max resp time:0.0;	stddev resp time:0.0]
]}ServerList:com.alibaba.cloud.nacos.ribbon.NacosServerList@5560ba5a
2025-08-22 13:29:21,629 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/optionsList/technicalCompetencyNumber
2025-08-22 13:29:22,561 INFO gateway [PollingServerListUpdater-0] c.n.config.ChainedDynamicProperty [ChainedDynamicProperty.java : 115] Flipping property: jx-mes-business-application.ribbon.ActiveConnectionsLimit to use NEXT property: niws.loadbalancer.availabilityFilteringRule.activeConnectionsLimit = 2147483647
2025-08-22 13:29:22,870 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_ticketLevel
2025-08-22 13:29:22,923 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_testMethodology
2025-08-22 13:29:22,974 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/test_data_dictionary_testType
2025-08-22 13:29:23,027 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_task_failureMode
2025-08-22 13:29:23,392 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionTask/productionTaskList
2025-08-22 14:52:00,647 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/PackageOrder
2025-08-22 14:52:28,594 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-22 14:52:28,610 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-22 14:52:28,706 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 14:52:28,898 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 14:52:32,377 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/PackageOrder
2025-08-22 14:52:56,057 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-22 14:52:56,067 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-22 14:52:56,113 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 14:52:56,261 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 14:53:24,817 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-22 14:53:24,828 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-22 14:53:24,877 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 14:53:25,082 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 14:53:27,964 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/PackageOrder
2025-08-22 14:54:05,262 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-22 14:54:05,273 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-22 14:54:05,319 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 14:54:05,486 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 14:54:08,045 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/PackageOrder
2025-08-22 14:54:12,484 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 14:54:12,484 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-22 14:54:12,484 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 14:54:12,484 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 14:54:12,669 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 14:54:12,783 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 14:54:12,888 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 14:54:55,285 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-22 14:54:55,292 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-22 14:54:55,339 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 14:54:55,462 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 14:54:56,994 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 14:54:56,995 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 14:54:56,995 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 14:54:56,995 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-22 14:54:57,171 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 14:54:57,276 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 14:54:57,429 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 14:58:45,366 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/themeConfig/detail
2025-08-22 14:58:45,381 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/config/configs
2025-08-22 14:58:45,415 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 14:58:45,539 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 14:58:46,914 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 14:58:46,915 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 14:58:46,918 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 14:58:46,920 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-22 14:58:46,935 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 14:58:47,117 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 14:58:47,198 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 14:58:47,366 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 15:00:05,304 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 15:00:10,809 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/logout
2025-08-22 15:00:11,987 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-08-22 15:00:16,044 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-08-22 15:00:16,244 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/user/profile
2025-08-22 15:00:16,303 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/menus
2025-08-22 15:00:17,286 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/login
2025-08-22 15:00:17,887 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/oauth/code
2025-08-22 15:00:25,422 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 15:00:25,428 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 15:00:25,428 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 15:00:25,429 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-22 15:00:25,446 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 15:00:25,602 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 15:00:25,692 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 15:00:25,740 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 15:00:27,605 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 15:00:28,862 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 15:03:46,520 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 15:03:48,856 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 15:44:02,776 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-08-22 15:44:48,100 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dic
2025-08-22 15:44:48,577 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-08-22 15:57:12,142 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dic
2025-08-22 15:57:12,407 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/dic/dics
2025-08-22 15:57:16,405 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/details/1958773560105373699
2025-08-22 16:17:15,547 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 16:17:15,575 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 16:17:15,587 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 16:17:15,598 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-22 16:17:15,735 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 16:17:15,917 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 16:17:16,040 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 16:20:02,907 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/importTemplate
2025-08-22 16:25:14,433 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/importTemplate
2025-08-22 16:25:17,827 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/importTemplate
2025-08-22 16:39:36,199 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 16:39:36,199 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-22 16:39:36,199 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 16:39:36,199 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 16:39:36,220 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 16:39:37,560 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 16:39:37,585 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 16:39:37,587 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 16:39:37,608 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/productionValueCalculationList
2025-08-22 16:39:37,666 INFO gateway [reactor-http-nio-5] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/formFilterConfig/detail/ProductionValueCalculation
2025-08-22 16:39:38,043 INFO gateway [reactor-http-nio-6] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 16:39:38,332 INFO gateway [reactor-http-nio-7] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_status
2025-08-22 16:39:38,386 INFO gateway [reactor-http-nio-8] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 16:39:38,465 INFO gateway [reactor-http-nio-1] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_type
2025-08-22 16:39:38,513 INFO gateway [reactor-http-nio-2] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 16:39:38,582 INFO gateway [reactor-http-nio-3] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/basic/api/detail/detail/production_value_calculation_testType
2025-08-22 16:40:02,903 INFO gateway [reactor-http-nio-4] c.h.frame.gate.filter.TokenFilter [TokenFilter.java : 51] url:/business/api/productionValueCalculation/importTemplate
