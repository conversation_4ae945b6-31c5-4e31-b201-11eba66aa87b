package com.huatek.frame.modules.business.service.dto;

import com.huatek.frame.modules.business.domain.vo.ProdTaskEqInfoVO;
import com.huatek.frame.modules.system.domain.BaseEntity;
import com.huatek.frame.common.utils.HuatekTools;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.springframework.format.annotation.DateTimeFormat;
import lombok.Data;
import java.sql.Timestamp;
import java.sql.Date;
import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

/**
* @description 产值计算DTO 实体类
* <AUTHOR>
* @date 2025-08-22
**/
@Data
@ApiModel("产值计算DTO实体类")
public class ProductionValueCalculationDTO extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;
    
    /**
	 * 主键ID
     **/
    @ApiModelProperty("主键ID")
    private String id;
    
    /**
	 * 工单编号
     **/
    @ApiModelProperty("工单编号")
    private String workOrderNumber;
    
    /**
	 * 工单类型
     **/
    @ApiModelProperty("工单类型")
    private String ticketType;
    
    /**
	 * 状态
     **/
    @ApiModelProperty("状态")
    private String status;
    
    /**
	 * 类型
     **/
    @ApiModelProperty("类型")
    private String type;
    
    /**
	 * 对账日期
     **/
    @ApiModelProperty("对账日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date settlementDate;
    
    /**
	 * 产品名称
     **/
    @ApiModelProperty("产品名称")
    private String productName;
    
    /**
	 * 产品型号
     **/
    @ApiModelProperty("产品型号")
    private String productModel;
    
    /**
	 * 生产厂家
     **/
    @ApiModelProperty("生产厂家")
    private String manufacturer;
    
    /**
	 * 批次号
     **/
    @ApiModelProperty("批次号")
    private String batchNumber;
    
    /**
	 * 产品分类
     **/
    @ApiModelProperty("产品分类")
    private String productCategory;
    
    /**
	 * 产品资料名称
     **/
    @ApiModelProperty("产品资料名称")
    private String productInformationName;
    
    /**
	 * 产品资料
     **/
    @ApiModelProperty("产品资料")
    private String productInformation1;
    
    /**
	 * 委托单位
     **/
    @ApiModelProperty("委托单位")
    private String entrustedUnit;
    
    /**
	 * 产品数量
     **/
    @ApiModelProperty("产品数量")
    private Long productQuantity;
    
    /**
	 * 收费标准名称
     **/
    @ApiModelProperty("收费标准名称")
    private String chargingStandardName;
    
    /**
	 * 内部核算价格
     **/
    @ApiModelProperty("内部核算价格")
    private BigDecimal internalAccountingPrice;
    
    /**
	 * 折扣
     **/
    @ApiModelProperty("折扣")
    private BigDecimal discount;
    
    /**
	 * 客户核算价格
     **/
    @ApiModelProperty("客户核算价格")
    private BigDecimal customerAccountingPrice;
    
    /**
	 * 对账价格
     **/
    @ApiModelProperty("对账价格")
    private BigDecimal settlementPrice;
    
    /**
	 * 内部价格分类
     **/
    @ApiModelProperty("内部价格分类")
    private String internalPriceClassification;
    
    /**
	 * 客户价格分类
     **/
    @ApiModelProperty("客户价格分类")
    private String customerPriceClassification;
    
    /**
	 * 订单编号
     **/
    @ApiModelProperty("订单编号")
    private String orderNumber;
    
    /**
	 * 结算单位
     **/
    @ApiModelProperty("结算单位")
    private String settlementUnit;
    
    /**
	 * 合格数
     **/
    @ApiModelProperty("合格数")
    private Long numberOfQualifiedProductions;
    
    /**
	 * 不合格数
     **/
    @ApiModelProperty("不合格数")
    private Long numNonQualProd;
    
    /**
	 * 不合格工序
     **/
    @ApiModelProperty("不合格工序")
    private String nonQualityProcess;
    
    /**
	 * 试验类型
     **/
    @ApiModelProperty("试验类型")
    private String testType;
    
    /**
	 * 委托日期
     **/
    @ApiModelProperty("委托日期")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dateOfEntrustment;
    
    /**
	 * 订单送检编号
     **/
    @ApiModelProperty("订单送检编号")
    private String orderInspectionNumber;
    
    /**
	 * 工单送检编号
     **/
    @ApiModelProperty("工单送检编号")
    private String workOrderInspectionNumber1;
    
    /**
	 * 对账单号
     **/
    @ApiModelProperty("对账单号")
    private String billStatementNumber;
    
    /**
	 * 合同编号
     **/
    @ApiModelProperty("合同编号")
    private String contractNumber;
    
    /**
	 * 创建人
     **/
    @ApiModelProperty("创建人")
    private String codexTorchCreatorId;
    
    /**
	 * 更新人
     **/
    @ApiModelProperty("更新人")
    private String codexTorchUpdater;
    
    /**
	 * 所属组织
     **/
    @ApiModelProperty("所属组织")
    private String codexTorchGroupId;
    
    /**
	 * 创建时间
     **/
    @ApiModelProperty("创建时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchCreateDatetime;
    
    /**
	 * 更新时间
     **/
    @ApiModelProperty("更新时间")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Timestamp codexTorchUpdateDatetime;
    
    /**
	 * 已删除
     **/
    @ApiModelProperty("已删除")
    private String codexTorchDeleted;



	/**
	 * 页码
	 */
	@ApiModelProperty("当前页码")
	private Integer page;
	
	/**
	 * 每页显示数量
	 */
	@ApiModelProperty("每页显示大小")
	private Integer limit;
}